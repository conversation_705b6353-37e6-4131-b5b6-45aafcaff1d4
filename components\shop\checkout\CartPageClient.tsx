"use client"
import { useState } from 'react';
import BasketCart from './BasketCart'
import FactorCard from './FactorCard'

const CartPageClient = () => {
  const [isNavigating, setIsNavigating] = useState(false);
  return (
    <div className='max-md:px-3 flex md:mt-16 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>

      <div className='md:w-[70%] max-md:w-full'>
        
        <BasketCart />
        {/* <PaymentMethodCard /> */}
        {/* <UserAddresses /> */}


        {/* <OrderSummary /> */}
      </div>

      <FactorCard
        steps={{ title: "cart", nextStepBtnTitle: "تایید و تکمیل سفارش", nextStepBtnLink: "/checkout/shipping" }}
        isNavigating={isNavigating}
        setIsNavigating={setIsNavigating}
      />
    </div>
  )
}

export default CartPageClient