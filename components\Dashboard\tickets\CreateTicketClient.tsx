"use client"

import { createTicket } from "@/actions/tickets.action"
import { DepartmentData } from "@/app/(dashboard)/dashboard/create-ticket/page"
import { Link } from "lucide-react"
import { useState } from "react"
import toast from "react-hot-toast"

const CreateTicketClient = ({ departments }: { departments: DepartmentData[] }) => {

    const [formData, setFormData] = useState({
        title: '',
        department_id: '',
        invoice_id: '',
        message: '',

    })
    const [profileImageFile, setProfileImageFile] = useState<File | null>(null)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isLoading, setIsLoading] = useState(true)

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { id, value } = e.target
        setFormData(prev => ({ ...prev, [id]: value }))
    }
    const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const { id, value } = e.target
        setFormData(prev => ({ ...prev, [id]: value }))
    }
    const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const { id, value } = e.target
        setFormData(prev => ({ ...prev, [id]: value }))
    }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (file) {
            setProfileImageFile(file)
        }
    }

    const handleSubmit = async () => {
        
        setIsSubmitting(true)
        const form = new FormData()

        if (formData.title !== undefined) form.append('title', formData.title)
        if (formData.department_id !== undefined) form.append('department_id', formData.department_id)
        if (formData.invoice_id !== undefined) form.append('invoice_id', formData.invoice_id)
        if (formData.message !== undefined) form.append('message', formData.message)



        if (profileImageFile) {
            form.append('profile_image', profileImageFile)
        }
        // Debug output
        for (const [key, val] of form.entries()) {
            console.log(`${key}:`, val)
        }

        try {
            const result = await createTicket(form)
            console.log('Server response:', result)
            toast.success(result.message)
        } catch (error) {
            console.error('Submit error:', error)
            toast.error("خطایی رخ داده است")
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <>
            <div className="mt-8 grid grid-cols-2 gap-5">
                <div className="flex flex-col gap-2">
                    <label htmlFor="department_id" className="font-medium text-gray-700">انتخاب دپارتمان</label>
                    <div className="relative">
                        <select
                            id="department_id"
                            className="w-full p-3 pr-10 border border-gray-300 rounded-xl focus:border-primary outline-none bg-white text-gray-700 appearance-none leading-[1.5]"
                            defaultValue=""
                            onChange={handleSelectChange}
                            disabled={isSubmitting}
                        >
                            <option value="" disabled>انتخاب دپارتمان</option>
                            {
                                departments.length ? departments?.map((department) => (
                                    <option key={department.id} value={department.id}>
                                        {department.title}
                                    </option>
                                ))
                                    :
                                    ""
                            }
                        </select>


                        <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none text-gray-500">
                            <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col gap-2">
                    <label htmlFor="invoice_id">شماره فاکتور</label>
                    <input
                        type="text"
                        id="invoice_id"
                        className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                        placeholder="شماره فاکتور (اختیاری)"
                        onChange={handleChange}
                        disabled={isSubmitting}
                    />
                </div>
                <div className="col-span-2 flex flex-col gap-2">
                    <label htmlFor="title">عنوان تیکت</label>
                    <input
                        type="text"
                        id="title"
                        className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                        placeholder="عنوان تیکت"
                        onChange={handleChange}
                        disabled={isSubmitting}
                    />
                </div>
                <div className="col-span-2 flex flex-col gap-2">
                    <label htmlFor="message"> محتوای تیکت </label>
                    <textarea
                        id="message"
                        className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                        placeholder=" متن پیام"
                        rows={4}
                        onChange={handleTextChange}
                        disabled={isSubmitting}
                    />
                </div>
            </div>
            <div className="mt-8 flex items-center gap-3">
                <button className="bg-[#F9FAFB] text-[#9DA5B0] py-3 px-4 w-36 border rounded-xl flex items-center gap-2 text-sm">
                    <Link size={16} /> انتخاب فایل
                </button>
                {isSubmitting ? (
                    <button
                        className="bg-primary text-white py-3 px-4 w-32 border rounded-xl flex items-center gap-2 text-sm cursor-not-allowed"
                        disabled
                    >
                        در حال ارسال...
                    </button>
                ) : (
                    <button onClick={handleSubmit} className="bg-primary text-white text-center justify-center py-3  w-32 border rounded-xl flex items-center gap-2 text-sm">
                        ایجاد تیکت
                    </button>
                )}


            </div>

        </>
    )
}

export default CreateTicketClient