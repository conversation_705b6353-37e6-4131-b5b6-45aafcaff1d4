import ProductPic from "@/public/assets/images/product.png"
import Image from 'next/image'
import { Eye, TrashIcon } from 'lucide-react'
import BasketCartIcon from '@/components/common/svg/BasketCartIcon'
const FavoritesPageItem = () => {
  return (
    <div className='w-[49%] border rounded-2xl flex gap-3 p-3'>
                <div className='relative h-[150px] w-[150px]'>
                    <Image src={ProductPic} alt='product-picture' fill className='object-contain' />
                </div>
                <div className='card-details'>
                    <h3 className='pb-3'>عنوان محصول</h3>
                    <h4 className='my-3 font-black text-black text-lg'>830,000 <span className='text-base font-thin text-[#62676e]'>تومان</span></h4>
                    <div className='flex gap-3 w-full justify-between'>
                        <button className='bg-[#F9FAFB]  py-3 px-4 w-44 border rounded-xl flex items-center gap-2 text-sm hover:opacity-80 transition-all duration-300 '> <BasketCartIcon /> افزودن به سبد خرید </button>
                        <button className='bg-[#F9FAFB]  p-3 border rounded-xl flex items-center gap-2 text-sm hover:opacity-80 transition-all duration-300'> <Eye /> </button>
                        <button className='bg-[#F9FAFB]  p-3 border rounded-xl flex items-center gap-2 text-sm hover:opacity-80 transition-all duration-300'> <TrashIcon /> </button>
                    </div>
                </div>
            </div>
  )
}

export default FavoritesPageItem