import { getFavorites } from "@/actions/favorites.action"
import FavoritesWrapper from "@/components/Dashboard/favorites/FavoritesWrapper"
import { ApiResponse, FavoritesResponseData } from "@/lib/types/favorites.types"
import "@/styles/styles.css"

import { Heart } from "lucide-react"


const FavoritesPage = async () => {
  const response: ApiResponse<FavoritesResponseData> = await getFavorites(1, 6)
  const favorites : FavoritesResponseData =  response.data 
  
  console.log(favorites);
   
  return (
    <section className="bg-white rounded-lg md:h-[48.5rem]">
        <div className="flex items-center gap-2 pb-5 border-b p-5 mb-5">
            <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-2 pt-3.5 rounded-b-full">
                <Heart className="" />
            </div>
                
            <h1>
                لیست علاقه مندی ها
            </h1>
        </div>
       <FavoritesWrapper favorites={favorites || []} />
    </section>
  )
}

export default FavoritesPage