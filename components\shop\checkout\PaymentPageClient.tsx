'use client';

import { useState } from 'react';
import PaymentMethodCard from './PaymentMethodCard';
import FactorCard from './FactorCard';
import OrderSummary from './OrderSummary';
import { CartApiItem, useCart } from '@/lib/context/cart-context';
import { createInvoice } from '@/actions/payment.action';
import { CreateInvoiceResponse } from '@/lib/types/invoice.types';
import toast from 'react-hot-toast';
import { invoicePayment } from '../../../actions/payment.action';
import { useRouter } from 'nextjs-toploader/app';
import DiscountCode from './DiscountCode';
import CompleteProfileBox, { convertPersianToEnglishNumbers } from './CompleteProfile';
import { UserProfileData } from '@/lib/types/types';
import { getUserProfile, updateUserProfile } from '@/actions/userProfile.action';

type PaymentPageClientProps = {
  orderSummaryData?: {
    items?: CartApiItem[];
    total?: number;
    total_discount?: number;
    user_id?: string;
    subtotal?: number;
  };
  addressId?: string;
  deliveryId?: string;
};

const PaymentPageClient = ({ orderSummaryData, addressId, deliveryId }: PaymentPageClientProps) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('online');
  const [discountCode, setDiscountCode] = useState<string>("")
  const [discountCodeInput, setDiscountCodeInput] = useState("");
  const [isNavigating, setIsNavigating] = useState(false);
  const [showCompleteProfile, setShowCompleteProfile] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<UserProfileData>({
          full_name: '',
          phone: '',
          email: '',
          national_code: '',
          shamsi_birth_date: '',
          // profile_image:''
      })
  const router = useRouter()
  const { cartItems } = useCart()
  const applyDiscount = (code: string) => {
    setDiscountCode(code);         
    toast.success("کد تخفیف اعمال شد"); 
  };

  const handleCreateInvoice = async () => {
    // setIsNavigating(true)
    if (cartItems.length == 0) {
      toast.error("سبد خرید خالی است. لطفا برای ادامه خرید محصولی به سبد خرید اضافه کنید", {
        duration: 10000
      })
      return
    }
    if (!deliveryId) {
      toast.error("لطفا روش ارسال را در مرحله قبل انتخاب کنید")
      return
    }
    
    if (addressId) {
      if (showCompleteProfile) {
        const submitProfileResponse = await handleSubmit()
        if (!submitProfileResponse.success) {
          setIsNavigating(false)
          return
        }
      }
      const response: CreateInvoiceResponse = await createInvoice(addressId, deliveryId ,discountCode ?? null);
      console.log(response);
      if (response.success) {
        if ('id' in response.data) {
          const payResponse = await invoicePayment(response.data.id, selectedPaymentMethod);
          console.log(payResponse);
          if (payResponse.success) {
            router.push(payResponse.data?.payment_link)            
          } else {
            // setIsNavigating(false)
            toast.error(payResponse.message)
          }
        } else {
          toast.error('Invoice ID not found in response.');
          // setIsNavigating(false)
        }
      } else {
        toast.error(response.message);
        if (response.status === 423) {
          setShowCompleteProfile(true)
          try {
                setIsLoading(true)
                const user = await getUserProfile()
                console.log(user);

                setFormData({
                    full_name: user?.data?.full_name || '',
                    phone: user?.data?.phone || '',
                    email: user?.data?.email || '',
                    national_code: user?.data?.national_code || '',
                    shamsi_birth_date: user?.data?.shamsi_birth_date || '',
                    // profile_image: user?.data?.profile_image || '',
                })
            } catch (error) {
                console.error('Error fetching user profile:', error)
                toast.error("خطا در بارگذاری اطلاعات کاربر")
            } finally {
                setIsLoading(false)
            }
        }
        // setIsNavigating(false)
      }
    } else {
      toast.error("لطفا آدرس خود را در مرحله انتخاب آدرس وارد کنید")
    }
    setIsNavigating(false)
  }

  const handlePaymentMethodChange = (method: string) => {
    setSelectedPaymentMethod(method);
  };
    const handleSubmit = async () => {
          // setIsSubmitting(true)
          const form = new FormData()
          
          if (formData.full_name !== undefined) form.append('fullname', formData.full_name)
          if (formData.phone !== undefined) form.append('phone', formData.phone)
          if (formData.email !== undefined) form.append('email', formData.email)
          if (formData.national_code !== undefined) form.append('national_code', formData.national_code)
          if (formData.shamsi_birth_date !== undefined) {
              const englishBirthDate = convertPersianToEnglishNumbers(formData.shamsi_birth_date)
              form.append('shamsi_birth_date', englishBirthDate)
          }
      
          for (const [key, val] of form.entries()) {
              console.log(`${key}:`, val)
          }
  
          const result = await updateUserProfile(form)
          if (result.success) {
            // setShowCompleteProfile(false)
            toast.success(result.message)
          } else {
            toast.error(result.message || "خطایی رخ داده است")            
          }
          return result
          
         
      }

  return (
    <div className='max-md:px-3 flex md:mt-16 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>
      <div className='md:w-[70%] max-md:w-full'>
        {
          showCompleteProfile ?
          <CompleteProfileBox formData={formData} setFormData={setFormData} isLoading={isLoading} />
          :
          <>
          <PaymentMethodCard
            onPaymentMethodChange={handlePaymentMethodChange}
          />
          <DiscountCode
            discountCodeInput={discountCodeInput}
            setDiscountCodeInput={setDiscountCodeInput}
            applyDiscount={applyDiscount} />

          {orderSummaryData && (
            <OrderSummary className="!max-md:hidden" {...orderSummaryData} />
          )}
          </>
        }
      </div>
      <FactorCard
        steps={{ title: "payment", nextStepBtnTitle: "پرداخت", nextStepBtnLink: "/checkout/result" }}
        paymentMethod={selectedPaymentMethod}
        onCreateInvoice={handleCreateInvoice}
        isNavigating={isNavigating}
        setIsNavigating={setIsNavigating}
        showCompleteProfile={showCompleteProfile}
      />
      {orderSummaryData && (
            <OrderSummary className="!md:hidden" {...orderSummaryData} />
          )}
    </div>
  );
};

export default PaymentPageClient;
