"use client"
import { getFavorites } from '@/actions/favorites.action';
import { FilterIcon, Search, SlidersHorizontal } from 'lucide-react';
import React, { useState } from 'react'
import FavoritesPageList from './FavoritesPageList';

const FavoritesWrapper = () => {
    const [activeFilter, setActiveFilter] = useState<'' | 'newest' | 'most_expensive' | 'most_cheapest'>('');
    const [filteredInvoices, setFilteredInvoices] = useState();
    const [loading, setLoading] = useState(false);



    const filters: { label: string; value: '' | 'newest' | 'most_expensive' | 'most_cheapest' }[] = [
        { label: 'همه', value: '' },
        { label: 'جدیدترین', value: 'newest' },
        { label: 'گرانترین', value: 'most_expensive' },
        { label: 'ارزانترین', value: 'most_cheapest' },
    ];

    const handleFilterClick = async (status: typeof activeFilter) => {
        setActiveFilter(status);
        // setLoading(true);
        // const query = status ? { delivery_status: status } : {};
        // const res = await getFavorites();
        // if (res.success) {
        //     // setFilteredInvoices(res.data.invoices);
        // }
        // setLoading(false);
    };
    // const counts = {
    //     all: allStatusCount,
    //     in_progress,
    //     sent,
    //     done,
    //     canceled,
    // };

    return (
        <div className="p-5">
            <div className="flex justify-between items-center w-full">
                <div className="flex flex-col md:flex-row gap-3 md:gap-5 items-center h-auto md:h-10 max-md:px-3 ">
                    <div className="flex items-center gap-2 max-md:hidden">
                        <SlidersHorizontal />
                        <span>مرتب سازی:</span>
                    </div>

                    <div className="hidden md:flex md:gap-8 h-full">
                        {filters.map(({ label, value }) => {
                            const isActive = activeFilter === value;
                            return (
                                <button
                                    key={value}
                                    onClick={() => handleFilterClick(value)}
                                    className={`transition-all ${isActive ? 'text-primary border-b-2 border-primary' : ''}`}
                                >
                                    {label}
                                    {/* <span className={`mr-2 rounded-full text-sm px-2 border ${isActive ? 'bg-[#F7BC06] text-black' : 'bg-[#F9FAFB] text-gray-500'}`}>
                                        {counts[countKey]}
                                    </span> */}
                                </button>
                            );
                        })}
                    </div>

                    {/* mobile version */}
                    <div className="w-full md:hidden">
                        <div className="relative w-full max-w-xs">

                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                                <FilterIcon className="w-5 h-5 text-gray-400" />
                            </div>

                            <select
                                value={activeFilter}
                                onChange={(e) => handleFilterClick(e.target.value as typeof activeFilter)}
                                className="w-full appearance-none border border-gray-300 rounded-xl py-2 pr-10 pl-4 text-sm text-gray-700 bg-white focus:ring-2 focus:ring-gray-200"
                            >
                                {filters.map(({ label, value }) => (
                                    <option key={value} value={value}>
                                        {label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>


                
            </div>
            <FavoritesPageList />
        </div>
    )
}

export default FavoritesWrapper