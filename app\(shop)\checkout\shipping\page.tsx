export const dynamic = 'force-dynamic';
import ShippingPageClient from "@/components/shop/checkout/ShippingPageClient"
import { DeliveryMethodsResponse, UserAddressesResponse } from "@/lib/types/types"
import { getDeliveryMethods, getUserAddresses } from "@/lib/utils"

const CheckoutShippingPage = async () => {
  const userAddresses:UserAddressesResponse = await getUserAddresses()
  const deliveryMethods: DeliveryMethodsResponse = await getDeliveryMethods() 
  console.log(deliveryMethods);
  

  return (
    <main className='container mx-auto  mb-16'>
       

       <ShippingPageClient userAddresses={userAddresses} deliveryMethods={deliveryMethods?.data || []} />

    </main>
  )
}

export default CheckoutShippingPage