"use server"

import { apiClient } from "@/lib/apiClient";


export async function getFavorites( page?: number , limit?: number, sort?: string ) {
    try {
        const response = await apiClient(`products/favorite?${page ? `?page=${page}` : ''}&${limit ? `&limit=${limit}` : ''}&${sort ? `&sort=${sort}` : ''}`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting favorites:", error);
        return { success: false, error: "Failed to get favorites" };
    }
}