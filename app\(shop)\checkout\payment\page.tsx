import PaymentPageClient from "@/components/shop/checkout/PaymentPageClient"
import { CartApiResponse } from "@/lib/context/cart-context"
import { getShoppingCart } from "@/lib/services/productService"
type PageProps = {
  searchParams: Promise<{
    address?: string;
    deliveryId?: string;
  }>
}
const CheckoutPaymentPage = async ({ searchParams }: PageProps) => {
  const {address, deliveryId} = await searchParams
  const OrderSummaryList:CartApiResponse = await getShoppingCart()
  console.log(address);
  
  return (
    <main className='container mx-auto mb-16'>     
      <PaymentPageClient addressId={address} orderSummaryData={OrderSummaryList.data} deliveryId={deliveryId} />
    </main>
  )
}

export default CheckoutPaymentPage