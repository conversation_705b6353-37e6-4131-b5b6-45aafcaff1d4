import { apiClient } from "@/lib/apiClient"
import { ArticlesApiResponse } from "@/lib/types/types"
import { Metadata } from "next"
import { CateoryResponse } from "@/lib/types/article.types"
import BlogMainContent from "@/components/blog/main/BlogMainContent"

export const metadata: Metadata = {
  robots: 'noindex, nofollow',
};


const BlogPage = async () => {
  const CategoriesResponse: CateoryResponse = await apiClient("categories")
    .then(res => res.json())
  const ArticlesResponse: ArticlesApiResponse = await apiClient(`articles`)
    .then(res => res.json())
    console.log(ArticlesResponse);
    
  return (
    <BlogMainContent ArticlesResponse={ArticlesResponse.data} CategoriesResponse={CategoriesResponse} />
  )

}

export default BlogPage