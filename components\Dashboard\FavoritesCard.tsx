import React from 'react'
import TrashIcon from '../common/svg/TrashIcon'
import EnterIcon from '../common/svg/EnterIcon'
import ProductPic from "@/public/assets/images/empty-cover.webp"
import Image from 'next/image'
import { FavoriteItem } from '@/lib/types/favorites.types'

const FavoritesCard = ({item}: {item: FavoriteItem}) => {
  return (
    <div className='p-5 max-md:p-3.5 bg-white md:max-w-xs md:rounded-3xl max-md:rounded-xl shadow-sm max-md:w-full max-md:mx-auto'>
        <div className="card-img relative h-[200px]">
            <Image src={ item?.image?.url || ProductPic} alt='product-picture' className='object-contain' fill />
        </div>
        <div className="card-body card-details">
            <h3 className='pb-2'> {item?.title} </h3>
            
            <h4 className='my-3 font-black text-black text-lg'> {item?.price?.toLocaleString()} <span className='text-base font-thin text-[#62676e]'>تومان</span></h4>
            <div className='mt-5 flex justify-between gap-3 items-center'>
                <button className='bg-[#F9FAFB]  py-3 px-4 w-24 border rounded-xl flex items-center gap-2 text-sm'> <TrashIcon /> حذف </button>
                <button className='bg-[#F9FAFB]  py-3 px-4 w-40 border rounded-xl flex justify-between items-center gap-2 text-sm'>  مشاهده <EnterIcon /></button>                
            </div>
        </div>
    </div>
  )
}

export default FavoritesCard