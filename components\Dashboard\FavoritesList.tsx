import Image from 'next/image'
import Ticket from "@/public/assets/images/ticket.png"
import FavoritesCard from './FavoritesCard'
import { FavoriteItem } from '@/lib/types/favorites.types'
import Link from 'next/link'


const FavoritesList = ( {favorites}: {favorites: FavoriteItem[]} ) => {
    return (
        <div>
            <div className="flex items-center w-full bg-gray-100 py-3  rounded-lg">

                <div className="flex items-center gap-2 text-gray-900 font-semibold text-lg">
                    <Image src={Ticket} alt='tocket-icon' />
                    <span>لیست علاقه‌مندی ها</span>
                </div>

                <div className="flex-1 border-t border-gray-300 mx-4"></div>

                <Link href={"/dashboard/favorites"} className="flex items-center gap-2 bg-gray-200 text-gray-600 px-4 py-2 rounded-full text-sm font-medium">
                    ≪ مشاهده همه
                </Link>
            </div>
            <div className='flex flex-wrap gap-5'>
                {favorites.map((item, index) => (
                    <FavoritesCard item={item} key={index} />
                ))}
                
            </div>
        </div>
    )
}

export default FavoritesList