"use client";

import { useEffect, useState } from "react";
import EditIcon from "../common/svg/EditIcon";
import SearchIcon from "../common/svg/SearchIcon";
import AlertIcon from "../common/svg/AlertIcon";
import { HeartIcon, Menu, UserIcon, X } from "lucide-react";
import Sidebar from "./Sidebar";
import {useAuth} from "@/lib/hooks/useAuth";
import Link from "next/link";
import { getUserProfile } from "@/actions/userProfile.action";
import Image from "next/image";

const DashboardNavbar = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [profilePicture, setProfilePicture] = useState(null)
  const { userData } = useAuth();
  useEffect(() => {
   async function fetchUser() {
    const user = await getUserProfile()
    setProfilePicture(user?.data?.profile_image)
    console.log(user?.data?.profile_image);
    
   }
   fetchUser()
  }, [])
  
    return (
        <>
            <nav
                className="bg-white shadow-md md:rounded-xl lg:py-2 px-5 lg:h-24 py-4 flex items-center justify-between">
                {/* User Profile */}
                <div className="user-profile flex items-center gap-4">
                    <div className="p-1 border-2 relative border-dashed border-gray-200 rounded-full">
                        {profilePicture ? (
                          <Image src={profilePicture} alt="profile" fill className="w-8 h-8 rounded-full" />
                          
                        ) : (
                            <UserIcon className="lg:w-10 lg:h-10 w-8 h-8"/>
                        )}
                        {/* <UserIcon className="lg:w-10 lg:h-10 w-8 h-8"/> */}
                    </div>
                    <div className="flex flex-col gap-1">
                        <div className="flex gap-4">
                            <span
                                className="lg:text-base text-sm font-bold"> {userData?.fullName || " بدون نام کاربری "} </span>
                            <Link href={'/dashboard/profile'}>
                                <EditIcon/>
                            </Link>
                        </div>
                        <span className="text-sm"> {userData?.phone} </span>
                    </div>
                </div>

        {/* Search & Icons */}
        <div className="flex gap-4 items-center lg:w-[28rem]">
          {/* Search Input (Hidden on Mobile) */}
          <div className="block relative w-full max-md:hidden">
            <input
              type="text"
              placeholder="جستجو..."
              className="w-full py-3 pl-4 pr-10 text-gray-500 bg-gray-100 rounded-full outline-none
                             focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
            />
            <SearchIcon className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 text-lg" />
          </div>

          {/* Buttons */}
          <div className="flex gap-2">
            {/* Menu Button on Mobile, HeartIcon on Desktop */}
            <button
              onClick={() => setIsSidebarOpen(true)}
              className="bg-gray-100 hover:bg-gray-200 transition-all rounded-full flex items-center justify-center w-10 h-10 lg:hidden"
            >
              <Menu className="size-6" />
            </button>

            {/* Heart Icon (Only on Desktop) */}
            <Link href={"/dashboard/favorites"} className="hidden lg:flex bg-gray-100 hover:bg-gray-200 transition-all rounded-full items-center justify-center w-10 h-10">
              <HeartIcon className="md:size-6 size-4" />
            </Link>

            {/* Alert Icon */}
            {/* دکمه اعلان */}
            <button
              onClick={() => setDrawerOpen(true)}
              className="bg-gray-100 hover:bg-gray-200 transition-all rounded-full flex items-center justify-center w-10 h-10"
            >
              <AlertIcon className="lg:size-6 size-4" />
            </button>

            {drawerOpen && (
              <div
                className="fixed inset-0 bg-black/30 z-40"
                onClick={() => setDrawerOpen(false)}
              />
            )}

            <div
              className={`fixed top-0 left-0 h-full w-80 max-w-full bg-white shadow-lg z-50 transform transition-transform duration-300 ${
                drawerOpen ? "translate-x-0" : "-translate-x-full"
              }`}
            >
              <div className="p-4 flex items-center justify-between border-b">
                <h2 className="text-lg font-semibold m-0">پیام های من</h2>
                <button
                  onClick={() => setDrawerOpen(false)}
                  className="text-sm text-gray-500 hover:text-black"
                >
                  <X size={18} />
                </button>
              </div>
              <div className="p-4 space-y-7 overflow-y-auto">
                <div>
                  <div className="flex justify-between items-center mb-5">
                    <div className="flex justify-start items-center gap-3">
                      <div className="h-full bg-primary rounded-full p-1">
                        <AlertIcon className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-xs">
                        <p className="mb-2">عنوان پیام و اعلان شما در اینجا</p>
                        <p>شنبه 3:8 ب.ظ</p>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-3">
                      <p className="p-1 bg-primary w-1 rounded-full text-left"></p>
                      <p className="text-xs">دو دقیقه پیش</p>
                    </div>
                  </div>
                  <div className="flex justify-end ">
                    <p className="text-xs w-[85%] bg-gray-200 p-1 px-3 rounded-xl text-justify leading-5">
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با  استفاده از طراحان گرافیک است. چاپگرها و متون
                    </p>
                  </div>
                </div>
                <div>
                <div className="flex justify-between items-center mb-3">
                    <div className="flex justify-start items-center gap-3">
                      <div className="h-full bg-gray-400 rounded-full p-1">
                        <AlertIcon className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-xs">
                        <p className="mb-2">عنوان پیام و اعلان شما در اینجا</p>
                        <p>شنبه 3:8 ب.ظ</p>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-3">
                      <p className="p-1 bg-primary w-1 rounded-full text-left"></p>
                      <p className="text-xs">دو دقیقه پیش</p>
                    </div>
                  </div>
                </div>
                <div>
                <div className="flex justify-between items-center mb-3">
                    <div className="flex justify-start items-center gap-3">
                      <div className="h-full bg-gray-400 rounded-full p-1">
                        <AlertIcon className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-xs">
                        <p className="mb-2">عنوان پیام و اعلان شما در اینجا</p>
                        <p>شنبه 3:8 ب.ظ</p>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-3">
                      <p className="p-1 bg-primary w-1 rounded-full text-left"></p>
                      <p className="text-xs">دو دقیقه پیش</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Sidebar Overlay & Animated Sidebar */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50"
          onClick={() => setIsSidebarOpen(false)}
        ></div>
      )}
      <div
        className={`fixed top-0 right-0 h-full bg-white w-80 shadow-lg z-50 transform ${
          isSidebarOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out`}
      >
        {/* Close Button */}
        {/* <button onClick={() => setIsSidebarOpen(false)} className="absolute top-4 left-4">
                    <X className="size-6" />
                </button> */}

        {/* Sidebar Component */}
        <Sidebar />
      </div>
    </>
  );
};

export default DashboardNavbar;
