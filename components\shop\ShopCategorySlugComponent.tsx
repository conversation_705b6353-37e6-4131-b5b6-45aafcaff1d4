'use client'

import SortingFilter from "@/components/shop/mainPage/SortBy";
import PriceRange from "@/components/shop/mainPage/PriceRange";
import CategoryFilter from "@/components/shop/mainPage/CategoryFilter";
import AvailableProductsFilter from "@/components/shop/mainPage/AvailableProductsFilter";
import WarrantyFilter from "@/components/shop/mainPage/WarrantyFilter";
import SelectColor from "@/components/shop/mainPage/SelectColor";
import {GenericResponse, ProductFilterOptions, ProductResponseData} from "@/lib/types/product.types";
import {useEffect, useMemo, useRef, useState} from "react";
import {useSearchParams} from "next/navigation";
import ProductFilterMobileDrawer from "@/components/shop/ProductFilterMobileDrawer";
import {useInfiniteQuery, useQuery} from "@tanstack/react-query";
// import {getProductsByCategory} from "@/lib/services/productService";
import {getProductsByCategory} from "@/actions/product.action";
import Pagination from "@/components/UI/Pagination";
import {Category, CategorySearchableAttributes} from "@/lib/types/category.types";
import MobileFilterDrawer from "@/components/shop/mainPage/MobileFilterDrawer";
import ProductsGrid from "@/components/shop/ProductsGrid";
import {toQueryString} from "@/utils/helpers";
import Breadcrumb from "@/components/common/BreadCrumb";
import DynamicAttributeFilters from "@/components/shop/mainPage/DynamicAttributeFilters";


type Props = {
    data?: GenericResponse<ProductResponseData>
    params: ProductFilterOptions
    isMobile: boolean
    categories?: Category[]
    categorySlug: string
    categoriesForBreadCrumb?: Category[]
    colors?: CategorySearchableAttributes
    filters: CategorySearchableAttributes[]
}

export default function ShopCategorySlugComponent({
                                                      data,
                                                      params,
                                                      isMobile,
                                                      categories,
                                                      categorySlug,
                                                      categoriesForBreadCrumb,
                                                      filters,
                                                      colors,
                                                  }: Props) {

    const [productParams, setProductParams] = useState<ProductFilterOptions>(params);
    const [openFilterDrawer, setOpenFilterDrawer] = useState(false)
    const [currentPage, setCurrentPage] = useState<number | undefined>(params.page)
    const [openCategoryDrawer, setOpenCategoryDrawer] = useState(false)
    const searchParams = useSearchParams()
    const observeRef = useRef<HTMLDivElement>(null);
    // const isMobile = useIsMobile(); // detect if mobile
    const [mobileUsePagination, setMobileUsePagination] = useState(false);
    const mobilePaginationThreshold = 10 * productParams.limit!

    const {page, ...productParamsWithNoPage} = productParams;
    const infiniteQuery = useInfiniteQuery({
        queryKey: ['product-infinite-sub', productParamsWithNoPage, categorySlug
        ],
        queryFn: async ({pageParam = 1}) => {
            const response = await getProductsByCategory(categorySlug, {...productParams, page: pageParam})
            return response
        },
        getNextPageParam: (lastPage, allPages) =>
            (+lastPage.data?.pagination.current_page < +lastPage.data?.pagination.last_page) ? allPages.length + 1 : undefined,
        initialPageParam: 1,
        staleTime: 5 * 60 * 1000, // 5 minutes in milliseconds
        placeholderData: (isMobile && !mobileUsePagination) ?
            {
                pages: [data!],
                pageParams: [1]
            }
            : undefined,
        enabled: (isMobile && !mobileUsePagination),
    })

    const paginatedQuery = useQuery({
        queryKey: ['products-sub', {...productParams}, categorySlug],
        queryFn: async () => {
            const response = await getProductsByCategory(categorySlug, productParams)
            return response
        },
        staleTime: 5 * 60 * 1000, // 5 minutes in milliseconds
        placeholderData: (data && (!isMobile || mobileUsePagination)) ? data : undefined,
        enabled: (!isMobile || mobileUsePagination),
    })
    
    useEffect(() => {
        console.log("paginatedQuery",paginatedQuery.data);
    }, [paginatedQuery])
    useEffect(() => {
        setProductParams(params)
    }, [params])

    const itemData = useMemo(() => {
        return (isMobile && !mobileUsePagination)
            ? infiniteQuery?.data?.pages?.[infiniteQuery.data.pages.length - 1]?.data
            : paginatedQuery?.data?.data;
    }, [isMobile, mobileUsePagination, infiniteQuery?.data, paginatedQuery?.data]);

    const items = useMemo(() => {
        if (isMobile) {
            if (mobileUsePagination) {
                return paginatedQuery?.data?.data?.products ?? [];
            } else {
                return infiniteQuery?.data?.pages?.flatMap((p) => p.data.products) ?? [];
            }
        }
        return paginatedQuery?.data?.data?.products ?? [];
    }, [isMobile, mobileUsePagination, paginatedQuery?.data, infiniteQuery?.data]);

    const itemsCount = useMemo(() => items?.length, [items]);

    useEffect(() => {
        if (!isMobile || mobileUsePagination || !infiniteQuery.hasNextPage) return;

        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && infiniteQuery.hasNextPage && !infiniteQuery.isFetchingNextPage) {
                    infiniteQuery.fetchNextPage();
                }
            },
            {threshold: 1}
        )
        const node = observeRef.current;

        if (node) observer.observe(node);

        return () => {
            if (node) {
                observer.unobserve(node);
            }
        };
    }, [infiniteQuery, mobileUsePagination, isMobile]);


    function handleResetFilters() {
        setProductParams({
            page: 1,
            in_stock_only: 'true',
            limit: 20,
        });
    }

    useEffect(() => {
        const queryString = toQueryString(productParams, mobileUsePagination);
        const searchParamsString = searchParams.toString();
        if (searchParamsString !== queryString) {
            window.history.replaceState(null, '', `?${queryString}`);
        }
        if (!(productParams.page! > 1 && !mobileUsePagination))
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
    }, [productParams, mobileUsePagination])

    useEffect(() => {
        if (isMobile && params.page) {
            setMobileUsePagination(true);
        }
    }, [isMobile, params.page]);

    useEffect(() => {
        setCurrentPage(productParams.page)
    }, [productParams.page]);

    useEffect(() => {
        if (isMobile) {
            if (itemsCount >= mobilePaginationThreshold) {
                const page = Math.ceil(itemsCount / productParams.limit!)
                setCurrentPage(page)
            }
        }
    }, [isMobile, itemsCount]);


    const productLoadingPagination = paginatedQuery.isLoading || paginatedQuery.isFetching || paginatedQuery.isPending

    const productLoadingInfinity = infiniteQuery.isPending || infiniteQuery.isLoading || infiniteQuery.isFetching

    return (
        <div className='w-full md:px-2'>
            <ProductFilterMobileDrawer
                showCloseBtn={false}
                isOpen={openFilterDrawer}
                onClose={() => setOpenFilterDrawer(false)}
            >
                <MobileFilterDrawer
                    productParams={productParams}
                    setProductParams={setProductParams}
                    colors={colors}
                    minPrice={itemData?.min_price || 0}
                    maxPrice={itemData?.max_price || 10000}
                    onFilterReset={handleResetFilters}
                    onClose={() => setOpenFilterDrawer(false)}
                    filters={filters}
                />
            </ProductFilterMobileDrawer>
            <ProductFilterMobileDrawer
                isOpen={openCategoryDrawer}
                onClose={() => setOpenCategoryDrawer(false)}
                showCloseBtn={false}
            >
                <CategoryFilter
                    open={true}
                    categories={categories || []}
                    onClose={() => setOpenCategoryDrawer(false)}
                    showCloseBtn={true}
                    showToggleBtn={false}
                />
            </ProductFilterMobileDrawer>
            <Breadcrumb categories={categoriesForBreadCrumb || []}/>
            <SortingFilter
                onFilterClick={() => setOpenFilterDrawer(true)}
                selectedSort={productParams.sort}
                setProductParams={setProductParams}
                totalCount={itemData?.pagination.total || 0}
                onResetFilters={handleResetFilters}
                onCategoryClick={() => setOpenCategoryDrawer(true)}
            />
            <div className='w-full flex items-start justify-between gap-8 max-w-7xl mx-auto my-6 flex-grow'>
                {/*Sidebar*/}
                <div className='md:col-span-2 flex flex-col gap-3 max-md:hidden !md:w-[32%] lg:w-[24%]'>
                    <PriceRange
                        min={itemData?.min_price || 0}
                        max={itemData?.max_price || 10000}
                        maxRange={productParams.max_price || itemData?.min_price || 0}
                        minRange={productParams.min_price || itemData?.max_price || 10000}
                        setProductParams={setProductParams}
                    />
                    <CategoryFilter
                        categories={categories || []}
                    />
                    <DynamicAttributeFilters
                        filters={filters}
                        productParams={productParams}
                        setProductParams={setProductParams}
                    />
                    <SelectColor
                        color={productParams.attribute_color}
                        onChange={(value: string) => {
                            setProductParams((prevState) =>
                                ({...prevState, attribute_color: value, page: 1}));
                        }}
                        colors={colors}
                    />
                    <AvailableProductsFilter
                        setProductParams={setProductParams}
                        value={productParams.in_stock_only}
                    />
                    <WarrantyFilter
                        setProductParams={setProductParams}
                        value={productParams.has_guarantee_only}
                    />
                </div>

                {/* Product list */}
                <div className='md:col-span-6 max-md:col-span-12 flex flex-1 flex-col max-md:px-3'>
                    {/*{(isMobile && itemData?.pagination.total && itemData.pagination.total > 0) &&*/}
                    {/*    <div className='lg:hidden flex text-sm pb-1 justify-between items-center'>*/}
                    {/*        <span></span>*/}
                    {/*        <span>{`${itemData?.pagination.total} محصول`}</span>*/}
                    {/*    </div>}*/}
                    {/* Products grid */}
                    <ProductsGrid
                        items={items}
                        isMobile={isMobile}
                        mobileUsePagination={mobileUsePagination}
                        hasMore={infiniteQuery.hasNextPage}
                        productLoadingPagination={productLoadingPagination}
                        productLoadingInfinity={productLoadingInfinity}
                        observeRef={observeRef as any}
                        itemsCount={itemsCount}
                        mobilePaginationThreshold={mobilePaginationThreshold}
                        isFetchingNextPage={infiniteQuery.isFetchingNextPage}
                    />

                    {(itemData?.pagination && itemData?.pagination.last_page > 1) &&
                        ((!isMobile || items.length >= mobilePaginationThreshold || mobileUsePagination)) &&
                        (!productLoadingPagination) &&
                        <div className='mt-auto w-full overflow-x-hidden my-[40px] md:mb-[60px] self-center'>
                            <Pagination
                                currentPage={Number(currentPage || 1)}
                                lastPage={itemData?.pagination.last_page || 0}
                                setCurrentPage={(page) => {
                                    setMobileUsePagination(true);
                                    setProductParams((prevState) => ({...prevState, page}))
                                }}
                            />
                        </div>}
                </div>
            </div>
        </div>
    );
}
