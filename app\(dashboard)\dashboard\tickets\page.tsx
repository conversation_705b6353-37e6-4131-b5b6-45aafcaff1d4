import ChatSmileIcon from "@/components/common/svg/ChatSmileIcon"
import FilterIcon from "@/components/common/svg/FilterIcon"
import { ChevronsLeft, Plus, Search } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import ChatSmile from "@/public/assets/images/chat-smile.png"
import ChatIcon from "@/components/common/svg/ChatIcon"
import ChatIconFilled from "@/components/common/svg/ChatIconFilled"
import MoreVerticalDots from "@/components/common/svg/MoreVerticalDots"
import { getUserTcikets } from "@/actions/tickets.action"
import { ApiResponse } from "@/lib/types/favorites.types"
import { TicketsResponse } from "@/lib/types/tickets.types"
import TicketsClient from "@/components/Dashboard/tickets/TicketsClient"



const TicketsPage = async () => {
    const response: ApiResponse<TicketsResponse> = await getUserTcikets()
    const tickets = response?.data?.tickets ?? []
    console.log(tickets);


    return (
        <section>
            <div className="w-full rounded-2xl bg-white flex justify-between items-center px-4 h-20">
                <div className="flex items-center gap-3">
                    <FilterIcon />
                    <Link href="#" >تیکت های پشتیبانی</Link>
                    <Link href="#" >تیکت های خریدار</Link>
                </div>
                <div className="flex items-center gap-3">
                    <p>
                        ایجاد تیکت
                    </p>
                    <Link href={"/dashboard/create-ticket"} className="p-2.5  bg-yellow text-white rounded-full">
                        <Plus />
                    </Link>
                </div>
            </div>
            {/* <div className="mt-8 flex justify-between">
                <div className="bg-white rounded-2xl p-4 w-[40%] h-[35rem]">
                    <div className="flex justify-between px-5">
                        <div className="flex items-center gap-2">
                            <ChatSmileIcon />
                            <p>تیکت ها</p>
                            <span> ({tickets.length}) </span>
                        </div>
                        <div>
                            <Image src={ChatSmile} alt="chat-smile" />
                        </div>
                    </div>
                   
                </div>
            </div> */}
                <TicketsClient tickets={tickets} />
        </section>
    )
}

export default TicketsPage