export interface FavoriteItemImage {
  url: string;
  caption: string;
}

export interface FavoriteItem {
  title: string;
  slug: string;
  rate: number;
  image: FavoriteItemImage;
  price: number;
  actualy_price: number;
  price_unit: string;
  in_stock: boolean;
  sale_price: number | null;
}

export interface FavoritesPagination {
  current_page: number;
  last_page: number;
  limit: number;
  total: number;
  from: number;
  to: number;
}

export interface FavoritesResponseData {
  products: FavoriteItem[]; 
  pagination: FavoritesPagination;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  status: number;
}
