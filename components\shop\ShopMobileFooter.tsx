'use client'

import Link from "next/link";
import {House, LayoutGrid, Search, ShoppingBasket} from "lucide-react";
import ProductFilterMobileDrawer from "@/components/shop/ProductFilterMobileDrawer";
import React, {useEffect, useState} from "react";
import CategoryFilter from "@/components/shop/mainPage/CategoryFilter";
import {Category} from "@/lib/types/category.types";
import SearchMobileInput from "@/components/shop/mainPage/SearchMobileInput";
import SearchMobileWrapper from "@/components/common/SearchMobileWrapper";
import {useCart} from "@/lib/context/cart-context";

type Props = {}

export default function ShopMobileFooter({}: Props) {
    // const [openCategoryDrawer, setOpenCategoryDrawer] = useState(false)

    const [openSearchDrawer, setOpenSearchDrawer] = useState(false)
    const {cartItems} = useCart()
 

    return (
        <>
            <ProductFilterMobileDrawer
                isOpen={openSearchDrawer}
                onClose={() => setOpenSearchDrawer(false)}
                showCloseBtn={false}
            >
                <SearchMobileWrapper
                    open={openSearchDrawer}
                    onClose={() => setOpenSearchDrawer(false)}
                />
            </ProductFilterMobileDrawer>
            <div
                className="lg:hidden inset-x-0 max-lg:block fixed w-full bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 h-[60px]">
                <div className="h-full w-full flex justify-around items-center">
                    <div className="flex border-complete-t w-full bg-neutral-000 z-5">
                        <Link
                            className="flex flex-col gap-y-1 lg:flex-row items-center flex-1 py-1 styles_navItem__mHCrk cursor-pointer"
                            href="/">
                            <div className="flex">
                                <House className='size-5'/>
                            </div>
                            <p className="text-caption text-xs text-neutral-500">خانه</p>
                        </Link>
                        <div
                            className="flex flex-col gap-y-1 items-center flex-1 py-1"
                        >
                            <div className="flex">
                                <LayoutGrid className='size-5'/>
                            </div>
                            <p className="text-caption text-xs text-neutral-500">دسته‌بندی</p>
                        </div>
                        <div
                            onClick={() => setOpenSearchDrawer(true)}
                            className="flex flex-col gap-y-1 lg:flex-row items-center flex-1 py-1 styles_navItem__mHCrk"
                        >
                            <div className="flex">
                                <Search className='size-5'/>
                            </div>
                            <p className="text-caption text-xs text-neutral-500">جستجو</p>
                        </div>
                        <Link
                            className="flex flex-col gap-y-1 lg:flex-row items-center flex-1 py-1 styles_navItem__mHCrk"
                            href="/checkout/cart">
                            <div className="flex relative">
                                <ShoppingBasket className='size-5'/>
                                <span className="absolute top-[3px] left-[-7px]">
                                <span
                                    className="bg-red-500 flex items-center justify-center p-0.2 w-4 h-4 text-sm rounded-full text-white">
                                    {cartItems.map(Item => Item.quantity).reduce((acc, curr) => acc + curr, 0)}
                                </span>
                            </span>
                            </div>
                            <p className="text-caption text-xs text-neutral-500">سبد خرید</p>
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );
}
